/*----------------------------------------------------------------------------------------------
*
* This file is ArcSoft's property. It contains ArcSoft's trade secret, proprietary and 		
* confidential information. 
* 
* The information and code contained in this file is only for authorized ArcSoft employees 
* to design, create, modify, or review.
* 
* DO NOT DISTRIBUTE, DO NOT DUPLICATE OR TRANSMIT IN ANY FORM WITHOUT PROPER AUTHORIZATION.
* 
* If you are not an intended recipient of this file, you must not copy, distribute, modify, 
* or take any action in reliance on it. 
* 
* If you have received this file in error, please immediately notify ArcSoft and 
* permanently delete the original and any copy of any file and any printout thereof.
*
*-------------------------------------------------------------------------------------------------*/
#ifndef ARCSOFT_RADAR_SIGNAL_H
#define ARCSOFT_RADAR_SIGNAL_H
#include "ArcTopicCommon.h"

#define RADAR_PROTOCOL_VER "*******"

#define RADAR_OD_MAX_SIZE (50)
#define RADAR_RDI_MAX_SIZE (0)

#pragma pack(4)

#define RARAR_RAW_CAN_CHN_VALID     ((uint32_t)1 << 0)
#define RARAR_RAW_CAN_EXT_VALID     ((uint32_t)1 << 1)
#define RARAR_RAW_CAN_CANID_VALID   ((uint32_t)1 << 2)
#define RARAR_RAW_CAN_DLC_VALID     ((uint32_t)1 << 3)
#define RARAR_RAW_CAN_DATA_VALID    ((uint32_t)1 << 4)

//雷达型号
typedef enum {
    RADAR_MODEL_UNKNOWN = 0,
    RADAR_MODEL_ARS408 = 1, //ARS408
    RADAR_MODEL_ARS410 = 2, //ARS410 
    RADAR_MODEL_ARS620 = 3,
    RADAR_MODEL_COUNT  = 4,
}RADAR_MODEL_T;

typedef enum {
    RADAR_TYPE_UNKNOWN = 0,
    RADAR_TYPE_FRONT = 1,
}RADAR_TYPE_T;

typedef enum {
    RADAR_MOTION_TYPE_MOVING              = 0,    //移动
    RADAR_MOTION_TYPE_STATIONART          = 1,    //静止
    RADAR_MOTION_TYPE_COMING              = 2,    //来向
    RADAR_MOTION_TYPE_POSSIBLD_STATIONART = 3,    //可能静止
    RADAR_MOTION_TYPE_UNKNOW              = 4,    //未知的
    RADAR_MOTION_TYPE_CRSOSS              = 5,    //横穿
    RADAR_MOTION_TYPE_CRSOSS_STATIONART   = 6,    //横穿静止
    RADAR_MOTION_TYPE_CRSOSS_MOVING       = 7,    //横穿移动
    RADAR_MOTION_TYPE_STOPPED             = 8,    //停止
    RADAR_MOTION_TYPE_RESERVE             = 9,    //预留
}RADAR_MOTION_TYPE_T;

//障碍物类型
typedef enum {
    RADAR_OBJECT_CLASS_UNKNOWN = 0,       //未知
    RADAR_OBJECT_CLASS_POINT = 1,       //点
    RADAR_OBJECT_CLASS_CAR = 2,           //汽车
    RADAR_OBJECT_CLASS_TRUCK = 3,         //卡车
    RADAR_OBJECT_CLASS_PEDESTRIAN = 4,    //行人
    RADAR_OBJECT_CLASS_MOTORCYCLE = 5,    //摩托车
    RADAR_OBJECT_CLASS_BICYCLE = 6,       //自行车
    RADAR_OBJECT_CLASS_BROARD = 7,        //宽广的
    RADAR_OBJECT_CLASS_RESERVE = 8,       //保留
}RADAR_OBJECT_CLASS_T;

typedef enum {
    RADAR_OBJECT_REF_POINT_REAR = 0,  //参考点位于物体背面的中点
    RADAR_OBJECT_REF_POINT_LEFT = 1,  // 参考点位于物体左侧的中点
    RADAR_OBJECT_REF_POINT_FRONT = 2, //参考点位于物体正面的中点
    RADAR_OBJECT_REF_POINT_RIGHT = 3, //参考点位于物体右侧的中点
}RADAR_OBJECT_REF_POINT_T;

typedef enum {
    RADAR_OBJECT_OBSTRUCTION_UNKNOWN = 0,        //对象的阻塞类型不明确
    RADAR_OBJECT_OBSTRUCTION_UNDER_DRIVABLE = 1,  //物体的高度高于自身车辆，自身可以安全通过
    RADAR_OBJECT_OBSTRUCTION_OVER_DRIVABLE = 2,    //物体的高度低于自身车辆，自身可以安全通过
    RADAR_OBJECT_OBSTRUCTION_OBSTACLE = 3,        //物体是障碍物，无法通过
}RADAR_OBJECT_OBSTRUCTION_TYPE_T;

typedef enum {
    RADAR_ROAD_TYPE_UNKNOWN = 0,      //未知
    RADAR_ROAD_TYPE_CITY = 1,         //城市
    RADAR_ROAD_TYPE_COUNTRY = 2,      //城市
    RADAR_ROAD_TYPE_HIGHWAY = 3,      //高速公路
}RADAR_ROAD_TYPE_T;

typedef enum {
    RADAR_CLUSTER_QUALITY_INVALID = 0, //无效
    RADAR_CLUSTER_QUALITY_LOW = 1,     //低
    RADAR_CLUSTER_QUALITY_MID = 2,     //中
    RADAR_CLUSTER_QUALITY_HIGH = 3,    //高
}RADAR_CLUSTER_QUALITY_T;

typedef enum {
    RADAR_ALIGNMENT_UNKNOW = 0,         //未知
    RADAR_ALIGNMENT_CALIBRATED = 1,     //已标定
    RADAR_ALIGNMENT_MISALIGNMENT = 2,   //未对准
    RADAR_ALIGNMENT_CALIBRATING = 3,    //标定中
    RADAR_ALIGNMENT_UNCALIBRATABLE = 4, //未标定
    RADAR_ALIGNMENT_RESERVED = 5,       //预留
}RADAR_ALIGNMENT_T;

typedef struct {
    uint8_t radarModel;        //雷达型号  RADAR_MODEL_T
    uint8_t radarType;        //雷达类型，安装位置，用途，编号等 RADAR_TYPE_T
    uint8_t isRadarWorking;       //雷达是否工作，0：未工作， 1：工作
    uint8_t isRadarBlocking;     //radar 是否Block。0：no Block， Block，
    uint8_t alignment;        //标定状态  RADAR_ALIGNMENT_T
    uint8_t isHWErr;             //硬件故障 0:无故障， 1：故障
    uint32_t latency;               //数据处理时间
    uint32_t timeStampGlobSec;		//RF任务完成时的全局时间戳，s部分
    uint32_t timeStampGlobNanoSec;    //RF任务完成时的全局时间戳，ns部分
    uint32_t valid;             //以上消息是否有效，从0bit开始，每bit表示一个参数
}ArcRadarState;


typedef struct {
    float vehicleHostSpeed;    //用于雷达物体跟踪的主机车辆对地速度 单位m/s
    float vehicleYawRate;    //车辆偏航率：单位rad/s
    float vehicleHostSpeedStd;   //车辆对地速度标准差 单位：m/s
    float vehicleAcceleration;   //车辆加速度 单位m/s^2
    float vehicleCurvature;   //算法过滤的曲线 单位：1/m
    uint32_t valid;             //以上消息是否有效，从0bit开始，每bit表示一个参数
}ArcVehicleInfo;

typedef struct {
    float radialRange;               //径向范围。 单位m
    float radialRelativeVelocity;    //径向相对速度。单位：m/s
    float azimuthAngle;              //方位角。单位：rad
    float elevationAngle;            //仰角。单位：rad
    float RCS;                       //雷达散射截面积。单位dBm^2
    float SNR;                       //信噪比。单位dB
    uint32_t dynamicPro;               //动态属性
    uint8_t clusterQuality;             //集群质量  CLUSTER_QUALITY_T
    uint32_t valid;                     //以上消息是否有效，从0bit开始，每bit表示一个参数
}ArcRDISingleFrame;


typedef struct {
    float roadClothoidParam_1;   //左右边界曲线的θ参数 单位rad
    float roadClothoidParam_2;   //左右边界曲线的𝐶0参数
    float roadClothoidParam_3;   //左右边界曲线的𝐶1参数
    float roadLatOffsetLeft;     //Y0左边界曲线
    float roadLatOffsetRight;    //Y0右边界曲线
    float roadXStartLeft;        //左边界曲线的纵向起点 单位m
    float roadXStartRight;       //右边界曲线的纵向起点 单位m
    float roadXEndLeft;          //左边界曲线的纵向末端 单位m
    float roadXEndRight;         //右边界曲线的纵向末端 单位m
    float roadConfidenceLeft;  //左边界曲线的置信度   单位%
    float roadConfidenceRight; //右边界曲线的置信度   单位%
    uint32_t roadNofLaneLeft;     //左侧至自车道的车道数
    uint32_t roadNofLaneRight;    //右侧至自车道的车道数
    float roadNofLaneProbLeft; //左侧车道数相对于本车道的可能性
    float roadNofLaneProbRight;//右侧车道数相对于本车道的可能性
    uint8_t roadType;               //道路类型   RADAR_ROAD_TYPE_T
    float roadTypeConf;         //道路类型置信度
    uint32_t valid;                 //以上消息是否有效，从0bit开始，每bit表示一个参数
}ArcRoadInfo;

typedef struct {
    uint32_t objId;                    //障碍物ID
    uint8_t isNewCreate;                  //是否为新创建的ID 0:previous 1:new
    uint8_t isObServedOrExtrapolated;     //物体是测量所得还是推测所得，0：测量。1:推测

    float existProbability;          //物体存在概率，单位%。（存在的概率，即成为真实目标的概率，而不是由多径等引起的传感器假象）
    float obstacleProbability;       //障碍物概率，单位%
    float MirrorProbability;         //物体镜像的概率

    float coordinate_X;              //障碍物纵向坐标，单位m
    float coordinate_Y;              //障碍物横向坐标，单位m
    float coordinate_Z;              //障碍物高度坐标，单位m

    float velRel_X ;                   //相对纵向速度，单位m/s
    float velRel_Y;                  //相对横向速度，单位m/s
    float accRel_X;                  //障碍物纵向相对加速度，单位m/s²
    float accRel_Y;                  //障碍物横向相对加速度，单位m/s²

    float velAbsSpeed_X;            //绝对纵向速度，单位m/s
    float velAbsSpeed_Y;            //绝对横向速度，单位m/s
    float accAbs_X;                 //障碍物纵向绝对加速度，单位m/s²
    float accAbs_Y;                 //障碍物横向绝对加速度，单位m/s²    

    float deviation_X ;               //纵向距离标准差，单位m
    float deviation_Y;               //横向距离标准差，单位m
    float deviation_Z;               //横向距离标准差，单位m
    float vrel_X;                    //纵向速度标准差，单位m/s
    float vrel_Y;                    //横向速度标准差，单位m/s
    float arelLat_X;                 //纵向加速度标准差，单位m/s²
    float arelLat_Y;                 //横向加速度标准差，单位m/s²

    float objLenght;                //障碍物长度
    float objWidth;                 //障碍物宽度
    float objOrientation;           //障碍物方向 rad
    float objOrientationStd;        //障碍物方向标准差
    float objYawRateAbs;            //障碍物绝对偏航角度 单位：rad
    float objYawRateAbsStd;         //障碍物绝对偏航角度标准差
    uint32_t objExistTime;          //障碍物存在时间，自首次检测到开始计时,单位ms
    float objSize;                  //障碍物面积(长x宽)，单位m^2

    float objRCS;                  //障碍物RCS
    uint8_t objRefPoint;          //障碍物参考点 OBJECT_REF_POINT_T
    uint8_t motionType;            //运动状态 MOTION_TYPE_T
    uint8_t objClass;              //障碍物类型，雷达对物体的分类可能不准确 OBJECT_CLASS_T
    uint8_t objObsType;             //阻塞类型 OBJECT_OBSTRUCTION_TYPE_T
    
    float objClassProbability;     //分类置信度
    uint64_t valid;                 //以上消息是否有效，从0bit开始，每bit表示一个参数
}ArcObjectInfo;

typedef struct {
    ArcRadarState radarState;        //雷达状态
    ArcVehicleInfo vehicleInfo;        //雷达输出的车辆信息
    ArcRoadInfo roadInfo;               //道路信息
    uint16_t objsNum;
    uint16_t RDINum;
    ArcObjectInfo objs[RADAR_OD_MAX_SIZE];        //物体信息
    ArcRDISingleFrame RDI[RADAR_RDI_MAX_SIZE];     //雷达探测接口
}ArcRadarInfos;

typedef struct{
    TopicHeader topicHeader;
    ArcRadarInfos radar;
}radar_mutiple_radar_infos_t;

typedef struct 
{
    uint8_t chn ;              //设备段通道号
    uint8_t ext;              //是否为扩展帧，0：否，1：是 
    uint8_t dlc;               //can 消息长度
    uint32_t canId;             //can id    
    uint8_t data[64];           //can数据
}radar_raw_can_t;

typedef struct
{
    TopicHeader topicHeader;
    uint16_t rawDataNum;
    uint32_t rawDataValid;
    radar_raw_can_t rawData[0];
}radar_raw_data_t;

#pragma pack()
#endif